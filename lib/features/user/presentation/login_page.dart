import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/core/enums/user_type.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'bloc/user_login_cubit.dart';
import 'bloc/user_login_state.dart';
import 'bloc/validate_login_cubit.dart';
import 'bloc/validate_login_state.dart';

@RoutePage()
class LoginPage extends StatelessWidget {
  final UserType? userType;

  const LoginPage({super.key, this.userType});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppInjector.get<UserLoginCubit>(),
      child: _LoginPageContent(userType: userType),
    );
  }
}

class _LoginPageContent extends StatefulWidget {
  final UserType? userType;

  const _LoginPageContent({this.userType});

  @override
  State<_LoginPageContent> createState() => _LoginPageContentState();
}

class _LoginPageContentState extends State<_LoginPageContent> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'LoginPage',
  );
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _obscurePassword = true;

  late ValidateLoginCubit _validateLoginCubit;

  @override
  void initState() {
    super.initState();
    _validateLoginCubit = AppInjector.get<ValidateLoginCubit>();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _validateLoginCubit.close();
    super.dispose();
  }

  // Validation methods
  String? _validateUsername(String? value, AppLocalizations l10n) {
    return _validateLoginCubit.validateField(
      fieldName: 'username',
      value: value ?? '',
      l10n: l10n,
    );
  }

  String? _validatePassword(String? value, AppLocalizations l10n) {
    return _validateLoginCubit.validateField(
      fieldName: 'password',
      value: value ?? '',
      l10n: l10n,
    );
  }

  void _onLoginPressed() {
    final l10n = AppLocalizations.of(context)!;

    if (_formKey.currentState!.validate()) {
      // Validate form using cubit
      _validateLoginCubit.validateLoginForm(
        username: _usernameController.text,
        password: _passwordController.text,
        l10n: l10n,
      );

      // If validation passes, proceed with login
      if (_validateLoginCubit.isFormValid) {
        final cubit = context.read<UserLoginCubit>();
        cubit.login(
          username: _usernameController.text,
          password: _passwordController.text,
          userType: widget.userType ?? UserType.member,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/icons/arrow-left.svg',
            colorFilter: const ColorFilter.mode(
              AppColors.textDefaultDark,
              BlendMode.srcIn,
            ),
          ),
          onPressed: () => context.router.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  24.h.verticalSpace,
                  Text(
                    l10n.login,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDefaultDark,
                      height: 1.5,
                    ),
                  ),
                  8.h.verticalSpace,
                  Text(
                    l10n.loginDescription,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textDefaultDark,
                      height: 1.5,
                    ),
                  ),
                  48.h.verticalSpace,
                  BlocBuilder<ValidateLoginCubit, ValidateLoginState>(
                    bloc: _validateLoginCubit,
                    builder: (context, state) {
                      return CustomTextField(
                        controller: _usernameController,
                        label: l10n.usernameLabel,
                        isRequired: true,
                        validator: (value) => _validateUsername(value, l10n),
                        forceErrorText: _validateLoginCubit.getFieldError(
                          'username',
                          l10n,
                        ),
                        onChanged: (value) {
                          // Clear field error when user starts typing
                          _validateLoginCubit.clearFieldError('username');
                        },
                      );
                    },
                  ),
                  16.h.verticalSpace,
                  BlocBuilder<ValidateLoginCubit, ValidateLoginState>(
                    bloc: _validateLoginCubit,
                    builder: (context, state) {
                      return CustomTextField(
                        controller: _passwordController,
                        label: l10n.passwordLabel,
                        isRequired: true,
                        obscureText: _obscurePassword,
                        validator: (value) => _validatePassword(value, l10n),
                        forceErrorText: _validateLoginCubit.getFieldError(
                          'password',
                          l10n,
                        ),
                        onChanged: (value) {
                          // Clear field error when user starts typing
                          _validateLoginCubit.clearFieldError('password');
                        },
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                          child: Container(
                            height: 32.w,
                            width: 32.w,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                            ),
                            child: Icon(
                              _obscurePassword
                                  ? Icons.visibility_off_outlined
                                  : Icons.visibility_outlined,
                              size: 16.w,
                              color: AppColors.iconDefault,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  16.h.verticalSpace,
                  Align(
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                      onTap: () {
                        // Implement forgot password functionality
                      },
                      child: Text(
                        l10n.forgotPassword,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ),
                  24.h.verticalSpace,
                  MultiBlocListener(
                    listeners: [
                      BlocListener<ValidateLoginCubit, ValidateLoginState>(
                        bloc: _validateLoginCubit,
                        listener: (context, state) {
                          // Handle validation state changes
                          if (state is ValidateLoginValid) {
                            // Validation passed, proceed with login
                            final cubit = context.read<UserLoginCubit>();
                            cubit.login(
                              username: _usernameController.text,
                              password: _passwordController.text,
                              userType: widget.userType ?? UserType.member,
                            );
                          }
                        },
                      ),
                      BlocListener<UserLoginCubit, UserLoginState>(
                        listener: (context, state) {
                          final cubit = context.read<UserLoginCubit>();

                          if (state.toString().contains('success')) {
                            // Navigate to main page on successful login
                            context.router.pushPath('/main');
                          } else if (cubit.isError) {
                            // Show error message
                            final errorMessage =
                                cubit.errorMessage ?? 'เกิดข้อผิดพลาด';
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(errorMessage),
                                backgroundColor: Colors.red,
                                action: SnackBarAction(
                                  label: 'ปิด',
                                  textColor: Colors.white,
                                  onPressed: () {
                                    ScaffoldMessenger.of(
                                      context,
                                    ).hideCurrentSnackBar();
                                  },
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ],
                    child: BlocBuilder<UserLoginCubit, UserLoginState>(
                      builder: (context, state) {
                        final cubit = context.read<UserLoginCubit>();

                        return PrimaryButton(
                          text: l10n.login,
                          width: double.infinity,
                          height: 52.h,
                          borderRadius: 50.r,
                          isLoading: cubit.isLoading,
                          onPressed: cubit.isLoading ? null : _onLoginPressed,
                        );
                      },
                    ),
                  ),
                  32.h.verticalSpace,
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 1,
                          color: AppColors.dividerColor,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Text(
                          l10n.or,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefaultDark,
                            height: 1.5,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 1,
                          color: AppColors.dividerColor,
                        ),
                      ),
                    ],
                  ),
                  24.h.verticalSpace,
                  Container(
                    width: double.infinity,
                    height: 52.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50.r),
                      border: Border.all(color: AppColors.dividerColor),
                    ),
                    child: TextButton(
                      onPressed: () {
                        context.router.pushPath('/how-to-login-thai-id');
                      },
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50.r),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/icons/thaid-logo.png',
                            width: 24.w,
                            height: 24.h,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            l10n.loginWithThaID,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFF0A3A57),
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  24.h.verticalSpace,
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          l10n.noAccountYet,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefault,
                            height: 1.5,
                          ),
                        ),
                        8.w.horizontalSpace,
                        GestureDetector(
                          onTap: () {
                            context.router.pushPath('/privacy-policy');
                          },
                          child: Text(
                            l10n.register,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textPrimary,
                              height: 1.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  24.h.verticalSpace,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
